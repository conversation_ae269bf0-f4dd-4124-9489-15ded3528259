package com.example.pertemuan9;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

public class MainActivity extends AppCompatActivity {
    GridView gview;
    ImageView imvBut;
    DatabaseHelper databaseHelper;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        imvBut=findViewById(R.id.imageButtonAddData);
        gview=findViewById(R.id.gridViewData);
        showExternalStoragepermission();
        databaseHelper=new DatabaseHelper(getApplicationContext());
        databaseHelper.getWritableDatabase();

        imvBut.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent=new Intent(MainActivity.this,ManageDataActivity.class);
                startActivity(intent);
            }
        });





    }
    private void showExternalStoragepermission(){
        String kindpermission="";
        if (Build.VERSION.SDK_INT>=33){
            kindpermission= android.Manifest.permission.READ_MEDIA_IMAGES;
        }else {
            kindpermission= Manifest.permission.READ_EXTERNAL_STORAGE;
        }
        int permissionCheck= ContextCompat.checkSelfPermission(this,kindpermission);

        if (permissionCheck!= PackageManager.PERMISSION_GRANTED){
            if (ActivityCompat.shouldShowRequestPermissionRationale(this,kindpermission)){
                //ini dilakukan  untuk user yang memilih denied sebemnya
            }else{
                //ini dilakukan jka belum mendapatkan permission
                requestPermission(kindpermission,1000);
            }
            //sudah melakukan
        }else{
            Toast.makeText(this, "Image Access Granted", Toast.LENGTH_SHORT).show();
        }

    }

    private void requestPermission(String permission, int permissionCode){
        ActivityCompat.requestPermissions(this,new String[]{permission},permissionCode);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults, int deviceId) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults, deviceId);
        if (requestCode==1000 ){
            if (grantResults.length > 0 && grantResults[0]==PackageManager.PERMISSION_GRANTED){
                Toast.makeText(this,"Permission Granted",Toast.LENGTH_SHORT).show();

            }else{
                Toast.makeText(this,"Permission Denied", Toast.LENGTH_SHORT).show();

            }
        }
    }
}