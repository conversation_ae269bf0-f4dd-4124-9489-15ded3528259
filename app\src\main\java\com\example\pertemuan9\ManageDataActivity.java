package com.example.pertemuan9;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import java.io.File;

public class ManageDataActivity extends AppCompatActivity {

    ImageView imvFoto;
    EditText etNama,etUmur,etNim;
    Button tchoosePic,btAdd,btUpdate,btDelete,btView;
    Mahasiswa data=null;
    String mode,path;
    DatabaseHelper databaseHelper=new DatabaseHelper(this);


    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_manage_data);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        imvFoto=findViewById(R.id.imageViewManageFotoSiswa);
        tchoosePic=findViewById(R.id.buttonChoosePicSiswa);

        tchoosePic.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //setting untuk mendapatkan image ke galeri dari OS android
                String path= Environment.getExternalStorageState()+ File.separator+"pictures"+File.separator;
                Uri uri=Uri.parse(path);
                Intent intent=new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                intent.setDataAndType(uri,"image/*");
                activityResultLauncher.launch(intent);

            }
        });
    }



    ActivityResultLauncher<Intent> activityResultLauncher=registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(), new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    Uri uri=result.getData().getData();
                    imvFoto.setImageURI(uri);
                }
            }
    );
}