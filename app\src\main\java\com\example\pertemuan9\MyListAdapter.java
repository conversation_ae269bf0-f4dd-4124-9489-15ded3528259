package com.example.pertemuan9;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;

public class MyListAdapter  extends ArrayAdapter<Mahasiswa> {
    Context context;
    int resource=0;
    ArrayList<Mahasiswa> arrMhs;

    public MyListAdapter(@NonNull Context context, int resource, @NonNull ArrayList<Mahasiswa> objects) {
        super(context, resource, objects);
        this.context=context;
        this.resource=resource;
        this.arrMhs=objects;
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        if (convertView==null){
         LayoutInflater inflater= LayoutInflater.from(context);
         convertView=inflater.inflate(resource,parent,false);
        }
        Mahasiswa myMhs=arrMhs.get(position);
        ImageView imv=convertView.findViewById(R.id.imageViewDetailDataSiswa);
        TextView tvNim=convertView.findViewById(R.id.textViewDetailDataSiswa);
        BitmapFactory.Options options=new BitmapFactory.Options();
        options.inSampleSize=10;
        Bitmap bmap= BitmapFactory.decodeFile(myMhs.getPath(),options);
        imv.setImageBitmap(bmap);
        tvNim.setText(myMhs.getNim());
        return super.getView(position, convertView, parent);
    }
}
