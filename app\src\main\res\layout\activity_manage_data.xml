<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ManageDataActivity">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imageViewManageFotoSiswa"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            tools:srcCompat="@tools:sample/avatars" />

        <Button
            android:id="@+id/buttonChoosePicSiswa"
            android:layout_width="match_parent"
            android:layout_height="61dp"
            android:text="Button" />

        <EditText
            android:id="@+id/editTextText"
            android:layout_width="400dp"
            android:layout_height="50dp"
            android:ems="10"
            android:inputType="text"
            android:text="Name" />

        <EditText
            android:id="@+id/editTextText2"
            android:layout_width="400dp"
            android:layout_height="50dp"
            android:ems="10"
            android:inputType="text"
            android:text="Name" />

        <EditText
            android:id="@+id/editTextText3"
            android:layout_width="400dp"
            android:layout_height="50dp"
            android:ems="10"
            android:inputType="text"
            android:text="Name" />

        <TableLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <Button
                    android:id="@+id/button6"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Button" />

                <Button
                    android:id="@+id/button7"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Button" />
            </TableRow>

            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <Button
                    android:id="@+id/button8"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Button" />

                <Button
                    android:id="@+id/button9"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:text="Button" />
            </TableRow>

            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">

            </TableRow>

            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </TableLayout>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>