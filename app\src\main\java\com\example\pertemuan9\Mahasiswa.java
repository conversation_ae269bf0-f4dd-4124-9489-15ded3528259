package com.example.pertemuan9;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

public class Mahasiswa implements Parcelable {
    private String Nim,Nama,Path;
    private int Umur;

    public Mahasiswa(String nim, String nama, String path, int umur) {
        Nim = nim;
        Nama = nama;
        Path = path;
        Umur = umur;
    }

    protected Mahasiswa(Parcel in) {
        Nim = in.readString();
        Nama = in.readString();
        Path = in.readString();
        Umur = in.readInt();
    }

    public static final Creator<Mahasiswa> CREATOR = new Creator<Mahasiswa>() {
        @Override
        public Mahasiswa createFromParcel(Parcel in) {
            return new Mahasiswa(in);
        }

        @Override
        public Mahasiswa[] newArray(int size) {
            return new Mahasiswa[size];
        }
    };

    public String getNim() {
        return Nim;
    }

    public String getNama() {
        return Nama;
    }

    public int getUmur() {
        return Umur;
    }

    public String getPath() {
        return Path;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeString(Nim);
        dest.writeString(Nama);
        dest.writeString(Path);
        dest.writeInt(Umur);
    }
}
